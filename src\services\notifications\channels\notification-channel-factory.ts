/**
 * Factory para criação de canais de notificação
 * Centraliza a configuração e instanciação dos diferentes canais
 */

import { EmailChannel } from './email/email-channel';
import { ResendProvider } from './email/providers/resend-provider';
import { ReactEmailEngine } from './email/react-email-engine';
import { NotificationConfigService } from './email/notification-config-service';
import type { NotificationChannelBase } from './base/notification-channel';
import type { NotificationChannel } from '../types/notification-types';

export class NotificationChannelFactory {
  private static emailChannel: EmailChannel | null = null;
  private static configService: NotificationConfigService | null = null;
  private static templateEngine: ReactEmailEngine | null = null;

  /**
   * Cria ou retorna uma instância do canal de e-mail
   */
  static async getEmailChannel(): Promise<EmailChannel> {
    if (!this.emailChannel) {
      // Obter configurações globais
      const globalConfig = NotificationConfigService.getGlobalConfig();
      
      // Validar configurações
      const validation = NotificationConfigService.validateGlobalConfig();
      if (!validation.isValid) {
        throw new Error(`Configurações inválidas: ${validation.errors.join(', ')}`);
      }

      // Criar provedor de e-mail baseado na configuração
      let emailProvider;
      
      switch (globalConfig.email.provider) {
        case 'resend':
          emailProvider = new ResendProvider({
            apiKey: globalConfig.email.resend.apiKey,
            fromDomain: globalConfig.email.resend.fromDomain,
            webhookSecret: globalConfig.email.resend.webhookSecret
          });
          break;
          
        case 'aws_ses':
          // TODO: Implementar AWS SES provider
          throw new Error('AWS SES provider não implementado ainda');
          
        default:
          throw new Error(`Provedor de e-mail não suportado: ${globalConfig.email.provider}`);
      }

      // Criar serviços auxiliares
      const configService = this.getConfigService();
      const templateEngine = this.getTemplateEngine();

      // Criar canal de e-mail
      this.emailChannel = new EmailChannel(emailProvider, templateEngine, configService);
    }

    return this.emailChannel;
  }

  /**
   * Cria ou retorna uma instância do serviço de configuração
   */
  static getConfigService(): NotificationConfigService {
    if (!this.configService) {
      this.configService = new NotificationConfigService();
    }
    return this.configService;
  }

  /**
   * Cria ou retorna uma instância do template engine
   */
  static getTemplateEngine(): ReactEmailEngine {
    if (!this.templateEngine) {
      this.templateEngine = new ReactEmailEngine();
    }
    return this.templateEngine;
  }

  /**
   * Obtém um canal específico por tipo
   */
  static async getChannel(channelType: NotificationChannel): Promise<NotificationChannelBase> {
    switch (channelType) {
      case 'email':
        return await this.getEmailChannel();
        
      case 'in_app':
        // TODO: Implementar canal in-app
        throw new Error('Canal in-app não implementado ainda');
        
      case 'whatsapp':
        // TODO: Implementar canal WhatsApp
        throw new Error('Canal WhatsApp não implementado ainda');
        
      default:
        throw new Error(`Canal não suportado: ${channelType}`);
    }
  }

  /**
   * Obtém múltiplos canais
   */
  static async getChannels(channelTypes: NotificationChannel[]): Promise<NotificationChannelBase[]> {
    const channels: NotificationChannelBase[] = [];
    
    for (const channelType of channelTypes) {
      try {
        const channel = await this.getChannel(channelType);
        channels.push(channel);
      } catch (error) {
        console.error(`Erro ao criar canal ${channelType}:`, error);
        // Continua com os outros canais mesmo se um falhar
      }
    }
    
    return channels;
  }

  /**
   * Valida se um canal está disponível e configurado
   */
  static async validateChannel(channelType: NotificationChannel): Promise<boolean> {
    try {
      const channel = await this.getChannel(channelType);
      return await channel.validateConfiguration();
    } catch (error) {
      console.error(`Erro ao validar canal ${channelType}:`, error);
      return false;
    }
  }

  /**
   * Valida múltiplos canais
   */
  static async validateChannels(channelTypes: NotificationChannel[]): Promise<Record<NotificationChannel, boolean>> {
    const results: Record<string, boolean> = {};
    
    for (const channelType of channelTypes) {
      results[channelType] = await this.validateChannel(channelType);
    }
    
    return results as Record<NotificationChannel, boolean>;
  }

  /**
   * Limpa o cache de instâncias (útil para testes)
   */
  static clearCache(): void {
    this.emailChannel = null;
    this.configService = null;
    this.templateEngine = null;
  }

  /**
   * Obtém informações sobre os canais disponíveis
   */
  static getAvailableChannels(): {
    channel: NotificationChannel;
    name: string;
    description: string;
    configured: boolean;
  }[] {
    const globalConfig = NotificationConfigService.getGlobalConfig();
    const validation = NotificationConfigService.validateGlobalConfig();
    
    return [
      {
        channel: 'email',
        name: 'E-mail',
        description: 'Notificações por e-mail usando Resend ou AWS SES',
        configured: validation.isValid && !!globalConfig.email.resend.apiKey
      },
      {
        channel: 'in_app',
        name: 'In-App',
        description: 'Notificações dentro da plataforma',
        configured: true // Sempre disponível
      },
      {
        channel: 'whatsapp',
        name: 'WhatsApp',
        description: 'Notificações via WhatsApp usando Evolution API',
        configured: !!globalConfig.whatsapp.apiKey && !!globalConfig.whatsapp.apiUrl
      }
    ];
  }

  /**
   * Testa o envio de uma notificação de teste
   */
  static async testChannel(
    channelType: NotificationChannel,
    testData: {
      tenantId: string;
      userId: string;
      title: string;
      message: string;
    }
  ): Promise<{ success: boolean; error?: string; messageId?: string }> {
    try {
      const channel = await this.getChannel(channelType);
      
      const result = await channel.send({
        tenantId: testData.tenantId,
        userId: testData.userId,
        type: 'system',
        title: testData.title,
        message: testData.message,
        data: { isTest: true }
      });

      return {
        success: result.success,
        error: result.error,
        messageId: result.messageId
      };

    } catch (error) {
      console.error(`Erro ao testar canal ${channelType}:`, error);
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }
}
