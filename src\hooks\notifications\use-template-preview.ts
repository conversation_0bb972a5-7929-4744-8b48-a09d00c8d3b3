/**
 * Hook para preview em tempo real de templates
 * Fornece renderização e validação instantânea usando Server Actions
 */

'use client';

import { useState, useCallback, useEffect } from 'react';
import { useQuery } from '@tanstack/react-query';
import { previewTemplate } from '@/src/app/(dashboard)/academia/configuracoes/templates/actions/template-preview-actions';
import { validateTemplate } from '@/src/app/(dashboard)/academia/configuracoes/templates/actions/template-actions';
import { TemplateEngine } from '@/services/notifications/templates/template-engine';


import type { TenantInfo } from '@/services/tenant/types';
import type {
  TemplatePreviewData,
  TemplateValidationResult,
  NotificationType
} from '@/services/notifications/types/notification-types';

interface UseTemplatePreviewReturn {
  // Estado do preview
  previewData: TemplatePreviewData | null;
  validation: TemplateValidationResult | null;
  loading: boolean;
  error: string | null;
  
  // Operações
  generatePreview: (templateId: string, variables: Record<string, any>) => Promise<void>;
  validateLive: (template: string, type: NotificationType) => Promise<void>;
  renderTemplate: (template: string, variables: Record<string, any>) => string;
  
  // Utilitários
  clearError: () => void;
  clearPreview: () => void;
}

export function useTemplatePreview(): UseTemplatePreviewReturn {
  const [previewData, setPreviewData] = useState<TemplatePreviewData | null>(null);
  const [validation, setValidation] = useState<TemplateValidationResult | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const clearError = useCallback(() => {
    setError(null);
  }, []);

  const clearPreview = useCallback(() => {
    setPreviewData(null);
    setValidation(null);
    setError(null);
  }, []);

  const generatePreview = useCallback(async (
    templateId: string,
    variables: Record<string, any>
  ) => {
    setLoading(true);
    setError(null);

    try {
      const result = await previewTemplate(templateId, variables);

      if (result.success) {
        setPreviewData(result.data!);
      } else {
        setError(result.error || 'Erro ao gerar preview');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    } finally {
      setLoading(false);
    }
  }, []);

  const validateLive = useCallback(async (
    template: string,
    type: NotificationType
  ) => {
    setError(null);

    try {
      const result = await validateTemplate(template, type);

      if (result.success) {
        setValidation(result.data!);
      } else {
        setError(result.error || 'Erro ao validar template');
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erro desconhecido');
    }
  }, []);

  const renderTemplate = useCallback((
    template: string,
    variables: Record<string, any>
  ): string => {
    try {
      const result = TemplateEngine.render(template, variables, {
        escapeHtml: true,
        allowMissingVariables: true
      });

      return result.rendered;
    } catch (err) {
      console.error('Erro ao renderizar template:', err);
      return template; // Retorna template original em caso de erro
    }
  }, []);

  return {
    // Estado
    previewData,
    validation,
    loading,
    error,
    
    // Operações
    generatePreview,
    validateLive,
    renderTemplate,
    
    // Utilitários
    clearError,
    clearPreview
  };
}

/**
 * Hook para debounce de validação em tempo real
 */
export function useTemplateValidationDebounced(
  template: string,
  type: NotificationType,
  delay: number = 500
): TemplateValidationResult | null {
  const [validation, setValidation] = useState<TemplateValidationResult | null>(null);

  useEffect(() => {
    if (!template.trim() || !type) {
      setValidation(null);
      return;
    }

    const timeoutId = setTimeout(async () => {
      try {
        const result = await validateTemplate(template, type);
        if (result.success) {
          setValidation(result.data!);
        }
      } catch (err) {
        console.error('Erro na validação debounced:', err);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [template, type, delay]);

  return validation;
}

/**
 * Hook para renderização em tempo real com debounce
 */
export function useTemplateRenderDebounced(
  template: string,
  variables: Record<string, any>,
  delay: number = 300
): string {
  const [rendered, setRendered] = useState('');

  useEffect(() => {
    if (!template.trim()) {
      setRendered('');
      return;
    }

    const timeoutId = setTimeout(() => {
      try {
        const result = TemplateEngine.render(template, variables, {
          escapeHtml: true,
          allowMissingVariables: true
        });
        setRendered(result.rendered);
      } catch (err) {
        console.error('Erro na renderização debounced:', err);
        setRendered(template);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [template, variables, delay]);

  return rendered;
}

/**
 * Função para buscar dados da academia via API (cliente-compatível)
 */
async function fetchAcademyData(): Promise<TenantInfo> {
  // Obter slug da URL atual
  const hostname = window.location.hostname;
  const slug = hostname.includes('localhost') ? 'vilhena' : hostname.split('.')[0];

  const response = await fetch(`/api/tenant/${slug}`);
  if (!response.ok) {
    throw new Error('Erro ao buscar dados da academia');
  }

  const data = await response.json();
  return {
    id: data.id,
    slug: slug,
    name: data.name,
    primaryColor: data.primary_color,
    secondaryColor: data.secondary_color,
    logoUrl: data.logo_url
  };
}

/**
 * Hook para obter dados da academia atual
 */
export function useAcademyData() {
  const { data: tenantInfo, isLoading, error } = useQuery<TenantInfo>({
    queryKey: ['tenant-info'],
    queryFn: fetchAcademyData,
    staleTime: 5 * 60 * 1000, // 5 minutos
    gcTime: 10 * 60 * 1000, // 10 minutos (nova propriedade no React Query v5)
  });

  return {
    academyData: tenantInfo ? {
      name: tenantInfo.name || 'Academia',
      logo_url: tenantInfo.logoUrl || '',
      primary_color: tenantInfo.primaryColor || '#333333',
      secondary_color: tenantInfo.secondaryColor || '#666666',
      slug: tenantInfo.slug || 'academia'
    } : null,
    loading: isLoading,
    error: error?.message || null
  };
}

/**
 * Hook para renderização em tempo real com dados da academia
 */
export function useTemplateRenderWithAcademy(
  template: string,
  variables: Record<string, any>,
  delay: number = 300
): { rendered: string; loading: boolean } {
  const [rendered, setRendered] = useState('');
  const { academyData, loading: academyLoading } = useAcademyData();

  useEffect(() => {
    if (!template.trim() || academyLoading) {
      setRendered('');
      return;
    }

    const timeoutId = setTimeout(() => {
      try {
        if (academyData) {
          const result = TemplateEngine.renderWithAcademyData(template, variables, academyData);
          setRendered(result.rendered);
        } else {
          // Fallback para renderização básica se dados da academia não estiverem disponíveis
          const result = TemplateEngine.render(template, variables, {
            escapeHtml: true,
            allowMissingVariables: true
          });
          setRendered(result.rendered);
        }
      } catch (err) {
        console.error('Erro na renderização com dados da academia:', err);
        setRendered(template);
      }
    }, delay);

    return () => clearTimeout(timeoutId);
  }, [template, variables, delay, academyData, academyLoading]);

  return {
    rendered,
    loading: academyLoading
  };
}
