/**
 * Engine de renderização de templates React Email
 * Suporta templates personalizáveis com variáveis dinâmicas
 */

import { render } from '@react-email/render';
import { createClient } from '@/services/supabase/server';
import type { NotificationTemplate, TemplateVariable } from '../../types/notification-types';

// Importar templates React Email
import { PaymentReminderTemplate } from './templates/payment-reminder-template';
import { PaymentDueSoonTemplate } from './templates/payment-due-soon-template';
import { PaymentDueTodayTemplate } from './templates/payment-due-today-template';
import { PaymentOverdueTemplate } from './templates/payment-overdue-template';
import { ClassReminderTemplate } from './templates/class-reminder-template';
import { SystemDefaultTemplate } from './templates/system-default-template';
import { AdminAlertTemplate } from './templates/admin-alert-template';
import { WelcomeTemplate } from './templates/welcome-template';
import { EventInvitationTemplate } from './templates/event-invitation-template';

// Importar mapeador de templates
import {
  getEmailTemplateType,
  mapTemplateDataToProps
} from './templates/template-mapper';

export interface RenderedTemplate {
  subject: string;
  body: string;
}

export interface RenderedEmail {
  subject: string;
  html: string;
  text: string;
}

export class ReactEmailEngine {
  /**
   * Renderiza um template de notificação in-app
   */
  async renderTemplate(templateId: string, variables: Record<string, any>): Promise<RenderedTemplate> {
    try {
      const supabase = await createClient();
      
      // Buscar template
      const { data: template, error } = await supabase
        .from('notification_templates')
        .select('*')
        .eq('id', templateId)
        .eq('is_active', true)
        .single();

      if (error || !template) {
        throw new Error(`Template não encontrado: ${templateId}`);
      }

      // Renderizar subject e body
      const subject = this.replaceVariables(template.subject_template || template.name, variables);
      const body = this.replaceVariables(template.body_template, variables);

      return {
        subject,
        body
      };

    } catch (error) {
      console.error('Erro ao renderizar template:', error);
      throw error;
    }
  }

  /**
   * Renderiza um template de e-mail usando React Email
   */
  async renderEmail(templateId: string, variables: Record<string, any>): Promise<RenderedEmail> {
    try {
      // Usar o template-mapper para determinar o tipo correto e mapear as props
      const notificationType = this.extractNotificationType(templateId);
      const emailTemplateType = getEmailTemplateType(notificationType, templateId, variables.overdueDays);
      const templateProps = mapTemplateDataToProps(emailTemplateType, variables);

      let reactElement;
      let subject = '';

      switch (emailTemplateType) {
        case 'payment-reminder':
          subject = `Lembrete de Pagamento - ${templateProps.planName || 'Mensalidade'}`;
          reactElement = PaymentReminderTemplate(templateProps);
          break;

        case 'payment-due-soon':
          subject = `Sua mensalidade vence em ${templateProps.daysUntilDue || 3} dias - ${templateProps.planName}`;
          reactElement = PaymentDueSoonTemplate(templateProps);
          break;

        case 'payment-due-today':
          subject = `Sua mensalidade vence HOJE - ${templateProps.planName}`;
          reactElement = PaymentDueTodayTemplate(templateProps);
          break;

        case 'payment-overdue':
          subject = `ATRASO ${templateProps.overdueDays} dias - ${templateProps.planName}`;
          reactElement = PaymentOverdueTemplate(templateProps);
          break;

        case 'class-reminder':
          subject = `Lembrete de Aula - ${templateProps.className || 'Treino'}`;
          reactElement = ClassReminderTemplate(templateProps);
          break;

        case 'welcome':
          subject = `Bem-vindo à ${templateProps.academyName}!`;
          reactElement = WelcomeTemplate(templateProps);
          break;

        case 'event-invitation':
          subject = `Convite: ${templateProps.eventName}`;
          reactElement = EventInvitationTemplate(templateProps);
          break;

        case 'admin-alert':
          subject = `Alerta Administrativo: ${templateProps.alertTitle}`;
          reactElement = AdminAlertTemplate(templateProps);
          break;

        case 'system-default':
        default:
          subject = templateProps.title || variables.title || 'Notificação do Sistema';
          reactElement = SystemDefaultTemplate({
            academyName: templateProps.academyName,
            academyLogo: templateProps.academyLogo,
            primaryColor: templateProps.primaryColor,
            secondaryColor: templateProps.secondaryColor,
            title: subject,
            message: templateProps.message || variables.message || 'Mensagem do sistema',
            userName: variables.userName || variables.studentName,
            data: variables.data,
            isDebug: variables.debug || variables.dispatcherTest || false,
            timestamp: variables.timestamp,
            environment: variables.environment,
            actionUrl: variables.actionUrl,
            actionText: variables.actionText,
            dashboardUrl: variables.dashboardUrl
          });
          break;
      }

      // Renderizar o componente React para HTML
      const html = await render(reactElement);
      const text = this.stripHtml(html);

      return {
        subject,
        html,
        text
      };

    } catch (error) {
      console.error('Erro ao renderizar e-mail:', error);

      // Para templates React padrão, não fazer fallback para banco de dados
      const notificationType = this.extractNotificationType(templateId);
      const emailTemplateType = getEmailTemplateType(notificationType, templateId, variables.overdueDays);

      if (emailTemplateType !== 'system-default') {
        // Re-throw o erro para templates React específicos
        throw error;
      }

      // Fallback para template simples apenas para templates genéricos
      return await this.renderSimpleEmail(templateId, variables);
    }
  }

  /**
   * Renderiza um template de WhatsApp
   */
  async renderWhatsApp(templateId: string, variables: Record<string, any>): Promise<string> {
    try {
      const rendered = await this.renderTemplate(templateId, variables);
      return this.stripHtml(rendered.body);

    } catch (error) {
      console.error('Erro ao renderizar WhatsApp:', error);
      throw error;
    }
  }

  /**
   * Extrai o tipo de notificação baseado no templateId
   */
  private extractNotificationType(templateId: string): 'payment' | 'class' | 'enrollment' | 'event' | 'system' {
    // Verificar templates específicos primeiro
    if (templateId.includes('payment')) return 'payment';
    if (templateId.includes('class')) return 'class';
    if (templateId.includes('enrollment')) return 'enrollment';
    if (templateId.includes('event')) return 'event';
    if (templateId.includes('system') || templateId.includes('admin')) return 'system';

    // Fallback: tentar extrair do padrão tipo_default
    const parts = templateId.split('_');
    if (parts.length > 0) {
      const firstPart = parts[0].toLowerCase();
      if (['payment', 'class', 'enrollment', 'event', 'system'].includes(firstPart)) {
        return firstPart as 'payment' | 'class' | 'enrollment' | 'event' | 'system';
      }
    }

    return 'system'; // fallback padrão
  }

  /**
   * Renderiza um template simples (fallback)
   */
  private async renderSimpleEmail(templateId: string, variables: Record<string, any>): Promise<RenderedEmail> {
    try {
      const rendered = await this.renderTemplate(templateId, variables);

      return {
        subject: rendered.subject,
        html: `
          <html>
            <head>
              <meta charset="utf-8">
              <meta name="viewport" content="width=device-width, initial-scale=1.0">
              <title>${rendered.subject}</title>
            </head>
            <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333; max-width: 600px; margin: 0 auto; padding: 20px;">
              <div style="background: #f8f9fa; padding: 20px; border-radius: 8px;">
                <h1 style="color: ${variables.primaryColor || '#007291'}; margin-bottom: 20px;">
                  ${variables.academyName || 'Academia'}
                </h1>
                <div style="background: white; padding: 20px; border-radius: 6px; box-shadow: 0 2px 4px rgba(0,0,0,0.1);">
                  ${rendered.body}
                </div>
                <p style="text-align: center; color: #666; font-size: 12px; margin-top: 20px;">
                  ${variables.academyName || 'Academia'} - Este e-mail foi enviado automaticamente.
                </p>
              </div>
            </body>
          </html>
        `,
        text: this.stripHtml(rendered.body)
      };

    } catch (error) {
      console.error('Erro ao renderizar template simples:', error);

      // Se falhar ao buscar template do banco, usar template de sistema padrão
      if (templateId === 'system_default' || templateId.includes('system')) {
        console.log('Usando template de sistema padrão como fallback');

        const subject = variables.title || 'Notificação do Sistema';
        const reactElement = SystemDefaultTemplate({
          academyName: variables.academyName || 'Academia',
          academyLogo: variables.academyLogo,
          primaryColor: variables.primaryColor,
          secondaryColor: variables.secondaryColor,
          title: variables.title || 'Notificação do Sistema',
          message: variables.message || 'Mensagem do sistema',
          userName: variables.userName || variables.studentName,
          data: variables.data,
          isDebug: variables.debug || variables.dispatcherTest || false,
          timestamp: variables.timestamp,
          environment: variables.environment,
          actionUrl: variables.actionUrl,
          actionText: variables.actionText,
          dashboardUrl: variables.dashboardUrl
        });

        const html = await render(reactElement);
        const text = this.stripHtml(html);

        return {
          subject,
          html,
          text
        };
      }

      throw error;
    }
  }

  /**
   * Substitui variáveis no template
   */
  private replaceVariables(template: string, variables: Record<string, any>): string {
    let result = template;

    // Substituir variáveis no formato {{variableName}}
    Object.entries(variables).forEach(([key, value]) => {
      const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
      result = result.replace(regex, String(value || ''));
    });

    return result;
  }

  /**
   * Remove tags HTML de uma string
   */
  private stripHtml(html: string): string {
    return html.replace(/<[^>]*>/g, '').trim();
  }

  /**
   * Valida se todas as variáveis obrigatórias estão presentes
   */
  async validateVariables(templateId: string, variables: Record<string, any>): Promise<{
    isValid: boolean;
    missingVariables: string[];
  }> {
    try {
      const supabase = await createClient();
      
      // Buscar template
      const { data: template } = await supabase
        .from('notification_templates')
        .select('type')
        .eq('id', templateId)
        .single();

      if (!template) {
        return { isValid: false, missingVariables: [] };
      }

      // Buscar variáveis obrigatórias para este tipo de template
      const { data: requiredVariables } = await supabase
        .from('template_variables')
        .select('variable_key')
        .eq('template_type', template.type)
        .eq('is_required', true);

      const missingVariables: string[] = [];
      
      requiredVariables?.forEach(variable => {
        if (!(variable.variable_key in variables) || variables[variable.variable_key] === null || variables[variable.variable_key] === undefined) {
          missingVariables.push(variable.variable_key);
        }
      });

      return {
        isValid: missingVariables.length === 0,
        missingVariables
      };

    } catch (error) {
      console.error('Erro ao validar variáveis:', error);
      return { isValid: false, missingVariables: [] };
    }
  }
}
