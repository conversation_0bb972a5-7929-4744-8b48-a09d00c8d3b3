/**
 * Implementação do provedor Resend para envio de e-mails
 * Utiliza a API do Resend para envio de e-mails transacionais
 */

import { Resend } from 'resend';
import {
  EmailProvider,
  EmailProviderBase,
  EmailData,
  EmailResult,
  BatchEmailResult,
  EmailDeliveryStatus,
  DomainVerification
} from './email-provider';

export interface ResendConfig {
  apiKey: string;
  fromDomain: string;
  webhookSecret?: string;
}

export class ResendProvider extends EmailProviderBase implements EmailProvider {
  private resend: Resend;
  private config: ResendConfig;

  constructor(config: ResendConfig) {
    super('resend');
    this.config = config;
    this.resend = new Resend(config.apiKey);
  }

  /**
   * Envia um e-mail através do Resend
   */
  async send(email: EmailData): Promise<EmailResult> {
    try {

      // Validar dados do e-mail
      const validation = this.validateEmailData(email);
      if (!validation.isValid) {
        console.error('🔍 [DEBUG] Validação falhou:', validation.errors);
        return {
          success: false,
          error: `Dados inválidos: ${validation.errors.join(', ')}`
        };
      }

      // Preparar dados para o Resend
      const resendData = {
        from: email.fromName ? `${email.fromName} <${email.from}>` : email.from,
        to: Array.isArray(email.to) ? email.to : [email.to],
        subject: email.subject,
        html: email.html,
        text: email.text,
        reply_to: email.replyTo,
        cc: email.cc ? (Array.isArray(email.cc) ? email.cc : [email.cc]) : undefined,
        bcc: email.bcc ? (Array.isArray(email.bcc) ? email.bcc : [email.bcc]) : undefined,
        attachments: email.attachments?.map(att => ({
          filename: att.filename,
          content: att.content,
          content_type: att.contentType,
          cid: att.cid
        })),
        headers: email.headers,
        tags: email.tags?.map(tag => ({ name: 'category', value: tag }))
      };


      const { data, error } = await this.resend.emails.send(resendData);

      if (error) {
        console.error('🔍 [DEBUG] Erro do Resend:', error);
        console.error('🔍 [DEBUG] Tipo do erro:', typeof error);
        console.error('🔍 [DEBUG] Erro completo:', JSON.stringify(error, null, 2));
        return {
          success: false,
          error: error.message || 'Erro desconhecido do Resend'
        };
      }

      return {
        success: true,
        messageId: data?.id,
        metadata: {
          provider: 'resend',
          timestamp: new Date().toISOString(),
          ...email.metadata
        }
      };

    } catch (error) {
      console.error('🔍 [DEBUG] Erro capturado no catch:', error);
      console.error('🔍 [DEBUG] Stack trace:', error instanceof Error ? error.stack : 'N/A');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Envia múltiplos e-mails em lote
   */
  async sendBatch(emails: EmailData[]): Promise<BatchEmailResult> {
    const results: EmailResult[] = [];
    let totalSent = 0;
    let totalFailed = 0;
    const errors: string[] = [];

    // Resend não tem API de lote nativa, então enviamos um por vez
    // Em produção, poderia implementar paralelização com Promise.allSettled
    for (const email of emails) {
      const result = await this.send(email);
      results.push(result);

      if (result.success) {
        totalSent++;
      } else {
        totalFailed++;
        if (result.error) {
          errors.push(result.error);
        }
      }
    }

    return {
      success: totalFailed === 0,
      results,
      totalSent,
      totalFailed,
      errors
    };
  }

  /**
   * Verifica o status de entrega de um e-mail
   */
  async getDeliveryStatus(messageId: string): Promise<EmailDeliveryStatus> {
    try {
      // Resend não tem API pública para status de entrega ainda
      // Por enquanto, retornamos status básico
      return {
        messageId,
        status: 'sent',
        timestamp: new Date().toISOString(),
        recipient: 'unknown',
        events: []
      };

    } catch (error) {
      console.error('Erro ao verificar status de entrega:', error);
      return {
        messageId,
        status: 'failed',
        timestamp: new Date().toISOString(),
        recipient: 'unknown',
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Verifica se um domínio está configurado corretamente
   */
  async verifyDomain(domain: string): Promise<DomainVerification> {
    try {
      // Resend tem API para verificação de domínio
      const { data, error } = await this.resend.domains.get(domain);

      if (error) {
        return {
          domain,
          verified: false,
          error: error.message
        };
      }

      return {
        domain,
        verified: data?.status === 'verified',
        dnsRecords: data?.records?.map((record: any) => ({
          type: record.type,
          name: record.name,
          value: record.value,
          status: record.status as 'verified' | 'pending' | 'failed'
        }))
      };

    } catch (error) {
      console.error('Erro ao verificar domínio:', error);
      return {
        domain,
        verified: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Valida se o provedor está configurado corretamente
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      // Testa a configuração fazendo uma chamada simples à API
      const { error } = await this.resend.domains.list();
      return !error;

    } catch (error) {
      console.error('Erro na validação da configuração Resend:', error);
      return false;
    }
  }

  /**
   * Retorna a configuração atual (sem dados sensíveis)
   */
  getConfig(): Omit<ResendConfig, 'apiKey' | 'webhookSecret'> {
    return {
      fromDomain: this.config.fromDomain
    };
  }
}
