/**
 * Template de e-mail para lembrete de aula
 * Personalizável com dados da academia e da aula
 */

import * as React from 'react';
import {
  BaseEmailTemplate,
  EmailHeading,
  EmailText,
  EmailButton,
  EmailDivider
} from './base-email-template';

export interface ClassReminderTemplateProps {
  // Dados da academia
  academyName: string;
  academyLogo?: string;
  primaryColor?: string;
  secondaryColor?: string;
  
  // Dados do estudante e aula
  studentName: string;
  className: string;
  instructorName: string;
  classDate: string;
  classTime: string;
  location?: string;
  duration?: number;
  maxStudents?: number;
  currentEnrollments?: number;
  
  // URLs opcionais
  classDetailsUrl?: string;
  cancelUrl?: string;
  rescheduleUrl?: string;
  
  // Configurações
  reminderType?: 'upcoming' | 'today' | 'starting_soon';
  requiresEquipment?: string[];
  specialInstructions?: string;
}

export function ClassReminderTemplate({
  academyName,
  academyLogo,
  primaryColor = '#007291',
  secondaryColor = '#004E89',
  studentName,
  className,
  instructorName,
  classDate,
  classTime,
  location,
  duration = 60,
  maxStudents,
  currentEnrollments,
  classDetailsUrl,
  cancelUrl,
  rescheduleUrl,
  reminderType = 'upcoming',
  requiresEquipment,
  specialInstructions
}: ClassReminderTemplateProps) {
  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('pt-BR', {
      weekday: 'long',
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    }).format(new Date(dateString));
  };

  const formatTime = (timeString: string) => {
    return timeString.substring(0, 5); // HH:MM
  };

  const getEmoji = () => {
    switch (reminderType) {
      case 'today': return '🥋';
      case 'starting_soon': return '⏰';
      default: return '📅';
    }
  };

  const getTitle = () => {
    switch (reminderType) {
      case 'today': return 'Sua Aula é Hoje!';
      case 'starting_soon': return 'Sua Aula Começa em Breve!';
      default: return 'Lembrete de Aula';
    }
  };

  const getMessage = () => {
    switch (reminderType) {
      case 'today':
        return 'Sua aula é hoje! Prepare-se e venha treinar conosco.';
      case 'starting_soon':
        return 'Sua aula começará em breve. É hora de se preparar!';
      default:
        return 'Você tem uma aula agendada. Não esqueça!';
    }
  };

  return (
    <BaseEmailTemplate
      academyName={academyName}
      academyLogo={academyLogo}
      primaryColor={primaryColor}
      secondaryColor={secondaryColor}
      previewText={`${getTitle()} - ${className} com ${instructorName}`}
    >
      <EmailHeading level={1} color={primaryColor}>
        {getEmoji()} {getTitle()}
      </EmailHeading>

      <EmailText>
        Olá <strong>{studentName}</strong>,
      </EmailText>

      <EmailText>
        {getMessage()}
      </EmailText>

      <EmailDivider color={primaryColor} />

      {/* Detalhes da aula */}
      <div style={{
        backgroundColor: '#f8fafc',
        padding: '24px',
        borderRadius: '8px',
        border: `1px solid ${primaryColor}20`
      }}>
        <EmailHeading level={3} color={primaryColor}>
          Detalhes da Aula
        </EmailHeading>

        <div style={{ marginBottom: '16px' }}>
          <EmailText variant="small" color="#64748b">
            <strong>Modalidade:</strong> {className}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Instrutor:</strong> {instructorName}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Data:</strong> {formatDate(classDate)}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Horário:</strong> {formatTime(classTime)}
          </EmailText>
          <EmailText variant="small" color="#64748b">
            <strong>Duração:</strong> {duration} minutos
          </EmailText>
          {location && (
            <EmailText variant="small" color="#64748b">
              <strong>Local:</strong> {location}
            </EmailText>
          )}
        </div>

        {/* Informações de lotação */}
        {maxStudents && currentEnrollments && (
          <div style={{
            backgroundColor: currentEnrollments >= maxStudents * 0.8 ? '#fef2f2' : '#f0fdf4',
            padding: '16px',
            borderRadius: '6px',
            border: `1px solid ${currentEnrollments >= maxStudents * 0.8 ? '#fecaca' : '#bbf7d0'}`,
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color={currentEnrollments >= maxStudents * 0.8 ? '#dc2626' : '#059669'}>
              <strong>Vagas:</strong> {currentEnrollments}/{maxStudents} ocupadas
              {currentEnrollments >= maxStudents * 0.8 && ' - Turma quase lotada!'}
            </EmailText>
          </div>
        )}

        {/* Equipamentos necessários */}
        {requiresEquipment && requiresEquipment.length > 0 && (
          <div style={{
            backgroundColor: '#fffbeb',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #fed7aa',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#d97706">
              <strong>🎒 Equipamentos necessários:</strong>
            </EmailText>
            <ul style={{ margin: '8px 0', paddingLeft: '20px' }}>
              {requiresEquipment.map((equipment, index) => (
                <li key={index} style={{ color: '#d97706', fontSize: '14px', marginBottom: '4px' }}>
                  {equipment}
                </li>
              ))}
            </ul>
          </div>
        )}

        {/* Instruções especiais */}
        {specialInstructions && (
          <div style={{
            backgroundColor: '#f0f9ff',
            padding: '16px',
            borderRadius: '6px',
            border: '1px solid #bae6fd',
            marginBottom: '16px'
          }}>
            <EmailText variant="small" color="#0369a1">
              <strong>📝 Instruções especiais:</strong> {specialInstructions}
            </EmailText>
          </div>
        )}
      </div>

      <EmailDivider />

      {/* Botões de ação */}
      <div style={{ textAlign: 'center', margin: '32px 0' }}>
        {classDetailsUrl && (
          <div style={{ marginBottom: '16px' }}>
            <EmailButton href={classDetailsUrl} primaryColor={primaryColor}>
              Ver Detalhes da Aula
            </EmailButton>
          </div>
        )}
        
        <div style={{ display: 'flex', gap: '12px', justifyContent: 'center', flexWrap: 'wrap' }}>
          {cancelUrl && (
            <EmailButton href={cancelUrl} primaryColor="#dc2626">
              Cancelar Presença
            </EmailButton>
          )}
          {rescheduleUrl && (
            <EmailButton href={rescheduleUrl} primaryColor={secondaryColor}>
              Reagendar
            </EmailButton>
          )}
        </div>
      </div>

      {/* Dicas e informações */}
      <EmailText variant="small" color="#64748b">
        <strong>💡 Dicas para a aula:</strong>
      </EmailText>
      <ul style={{ color: '#64748b', fontSize: '14px', lineHeight: '1.5', paddingLeft: '20px' }}>
        <li>Chegue 10-15 minutos antes para se aquecer</li>
        <li>Traga uma garrafa de água</li>
        <li>Use roupas confortáveis para treinar</li>
        {reminderType === 'starting_soon' && (
          <li>Evite refeições pesadas 2 horas antes da aula</li>
        )}
      </ul>

      {reminderType === 'starting_soon' && (
        <div style={{
          backgroundColor: '#fef2f2',
          padding: '16px',
          borderRadius: '6px',
          border: '1px solid #fecaca',
          marginTop: '24px'
        }}>
          <EmailText variant="small" color="#dc2626">
            <strong>⏰ Atenção:</strong> Sua aula começará em breve! 
            Se você não conseguir comparecer, cancele sua presença para 
            liberar a vaga para outros alunos.
          </EmailText>
        </div>
      )}
    </BaseEmailTemplate>
  );
}
