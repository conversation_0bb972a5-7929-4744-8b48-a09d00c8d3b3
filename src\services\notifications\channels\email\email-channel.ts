/**
 * Canal de e-mail para o sistema de notificações
 * Utiliza provedores de e-mail para envio de notificações
 */

import { NotificationChannelBase, NotificationChannelData, NotificationChannelResult, BatchNotificationResult, DeliveryStatus } from '../base/notification-channel';
import { EmailProvider, EmailData } from './providers/email-provider';
import { ResendProvider } from './providers/resend-provider';
import { ReactEmailEngine } from './react-email-engine';
import { NotificationConfigService } from './notification-config-service';
import { NotificationPermissionService } from '../../core/notification-permission-service';
import { createAdminClient } from '@/services/supabase/server';

export class EmailChannel extends NotificationChannelBase {
  private provider: EmailProvider;
  private templateEngine: ReactEmailEngine;
  private configService: NotificationConfigService;
  private permissionService: NotificationPermissionService;

  constructor(provider: EmailProvider, templateEngine: ReactEmailEngine, configService: NotificationConfigService) {
    super('email');
    this.provider = provider;
    this.templateEngine = templateEngine;
    this.configService = configService;
    this.permissionService = new NotificationPermissionService();
  }

  /**
   * Envia uma notificação por e-mail
   */
  async send(data: NotificationChannelData): Promise<NotificationChannelResult> {
    try {

      const permission = await this.permissionService.checkPermission({
        tenantId: data.tenantId,
        userId: data.userId,
        notificationType: data.type,
        channel: 'email'
      });


      if (!permission.allowed) {
        console.log('🔍 [DEBUG] Permissão negada para e-mail');
        return {
          success: false,
          error: permission.reason || 'E-mail não permitido para este usuário/tipo'
        };
      }


      const tenantConfig = await this.configService.getTenantConfig(data.tenantId);


      if (!tenantConfig.emailEnabled) {
        console.log('🔍 [DEBUG] E-mail desabilitado para este tenant');
        return {
          success: false,
          error: 'E-mail desabilitado para este tenant'
        };
      }


      const userEmail = await this.getUserEmail(data.userId);
      if (!userEmail) {
        return {
          success: false,
          error: 'E-mail do usuário não encontrado'
        };
      }

      // Renderizar template de e-mail
      const templateId = data.templateId || `${data.type}_default`;


      const renderedEmail = await this.templateEngine.renderEmail(
        templateId,
        {
          ...data.variables,
          academyName: tenantConfig.tenantName,
          academyLogo: tenantConfig.academyLogo,
          primaryColor: tenantConfig.primaryColor,
          secondaryColor: tenantConfig.secondaryColor,
          tenantSlug: tenantConfig.tenantSlug
        }
      );

      // Preparar dados do e-mail
      const emailData: EmailData = {
        to: userEmail,
        from: tenantConfig.emailFromDomain,
        fromName: tenantConfig.emailFromName,
        subject: renderedEmail.subject,
        html: renderedEmail.html,
        text: renderedEmail.text,
        metadata: {
          tenantId: data.tenantId,
          userId: data.userId,
          notificationType: data.type,
          templateId: data.templateId
        }
      };


      const result = await this.provider.send(emailData);

      return {
        success: result.success,
        messageId: result.messageId,
        error: result.error,
        metadata: {
          provider: this.provider.getProviderName(),
          ...result.metadata
        }
      };

    } catch (error) {
      console.error('🔍 [DEBUG] Erro capturado no EmailChannel:', error);
      console.error('🔍 [DEBUG] Stack trace:', error instanceof Error ? error.stack : 'N/A');
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Envia múltiplas notificações por e-mail em lote
   */
  async sendBatch(notifications: NotificationChannelData[]): Promise<BatchNotificationResult> {
    const results: NotificationChannelResult[] = [];
    let totalSent = 0;
    let totalFailed = 0;
    const errors: string[] = [];

    // Agrupar por tenant para otimizar configurações
    const notificationsByTenant = new Map<string, NotificationChannelData[]>();
    
    for (const notification of notifications) {
      const tenantNotifications = notificationsByTenant.get(notification.tenantId) || [];
      tenantNotifications.push(notification);
      notificationsByTenant.set(notification.tenantId, tenantNotifications);
    }

    // Processar cada tenant
    for (const [tenantId, tenantNotifications] of notificationsByTenant) {
      try {
        const tenantConfig = await this.configService.getTenantConfig(tenantId);
        
        if (!tenantConfig.emailEnabled) {
          // Marcar todas as notificações deste tenant como falha
          for (const notification of tenantNotifications) {
            results.push({
              success: false,
              error: 'E-mail desabilitado para este tenant'
            });
            totalFailed++;
            errors.push('E-mail desabilitado para este tenant');
          }
          continue;
        }

        // Preparar e-mails para este tenant
        const emailsToSend: EmailData[] = [];
        
        for (const notification of tenantNotifications) {
          try {
            const userEmail = await this.getUserEmail(notification.userId);
            if (!userEmail) {
              results.push({
                success: false,
                error: 'E-mail do usuário não encontrado'
              });
              totalFailed++;
              errors.push('E-mail do usuário não encontrado');
              continue;
            }

            const renderedEmail = await this.templateEngine.renderEmail(
              notification.templateId || `${notification.type}_default`,
              {
                ...notification.variables,
                academyName: tenantConfig.tenantName,
                academyLogo: tenantConfig.academyLogo,
                primaryColor: tenantConfig.primaryColor,
                secondaryColor: tenantConfig.secondaryColor,
                tenantSlug: tenantConfig.tenantSlug
              }
            );

            emailsToSend.push({
              to: userEmail,
              from: tenantConfig.emailFromDomain,
              fromName: tenantConfig.emailFromName,
              subject: renderedEmail.subject,
              html: renderedEmail.html,
              text: renderedEmail.text,
              metadata: {
                tenantId: notification.tenantId,
                userId: notification.userId,
                notificationType: notification.type,
                templateId: notification.templateId
              }
            });

          } catch (error) {
            results.push({
              success: false,
              error: error instanceof Error ? error.message : 'Erro ao preparar e-mail'
            });
            totalFailed++;
            errors.push(error instanceof Error ? error.message : 'Erro ao preparar e-mail');
          }
        }

        // Enviar e-mails em lote para este tenant
        if (emailsToSend.length > 0) {
          const batchResult = await this.provider.sendBatch(emailsToSend);
          results.push(...batchResult.results);
          totalSent += batchResult.totalSent;
          totalFailed += batchResult.totalFailed;
          errors.push(...batchResult.errors);
        }

      } catch (error) {
        // Erro ao processar tenant
        for (const notification of tenantNotifications) {
          results.push({
            success: false,
            error: error instanceof Error ? error.message : 'Erro ao processar tenant'
          });
          totalFailed++;
        }
        errors.push(error instanceof Error ? error.message : 'Erro ao processar tenant');
      }
    }

    return {
      success: totalFailed === 0,
      results,
      totalSent,
      totalFailed,
      errors
    };
  }

  /**
   * Verifica o status de entrega de um e-mail
   */
  async getDeliveryStatus(messageId: string): Promise<DeliveryStatus> {
    try {
      const status = await this.provider.getDeliveryStatus(messageId);
      
      return {
        messageId: status.messageId,
        status: status.status,
        timestamp: status.timestamp,
        error: status.error,
        metadata: {
          provider: this.provider.getProviderName(),
          recipient: status.recipient,
          events: status.events
        }
      };

    } catch (error) {
      console.error('Erro ao verificar status de entrega:', error);
      return {
        messageId,
        status: 'failed',
        timestamp: new Date().toISOString(),
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      };
    }
  }

  /**
   * Valida se o canal está configurado corretamente
   */
  async validateConfiguration(): Promise<boolean> {
    try {
      return await this.provider.validateConfiguration();
    } catch (error) {
      console.error('Erro na validação do canal de e-mail:', error);
      return false;
    }
  }

  /**
   * Obtém o e-mail do usuário
   */
  private async getUserEmail(userId: string): Promise<string | null> {
    try {
      const supabase = await createAdminClient();

      // Buscar e-mail do usuário na tabela users
      const { data: user, error } = await supabase
        .from('users')
        .select('email')
        .eq('id', userId)
        .single();

      if (error) {
        console.error('Erro ao buscar usuário:', error);
        return null;
      }

      return user?.email || null;
    } catch (error) {
      console.error('Erro ao buscar e-mail do usuário:', error);
      return null;
    }
  }
}
