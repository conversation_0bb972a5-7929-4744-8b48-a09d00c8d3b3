/**
 * Teste simples para verificar a integração das cron jobs
 * Execute este arquivo para testar se tudo está funcionando
 */

import { NotificationDispatcher } from './channels/notification-dispatcher'

/**
 * Teste básico do NotificationDispatcher
 */
export async function testNotificationDispatcher() {
  console.log('🧪 Testando NotificationDispatcher...')
  
  try {
    const dispatcher = new NotificationDispatcher()
    
    // Teste de envio de notificação simples
    const result = await dispatcher.dispatch({
      tenantId: 'test-tenant',
      userId: 'test-user',
      type: 'system',
      category: 'info',
      priority: 'medium',
      title: 'Teste de Integração',
      message: 'Esta é uma notificação de teste para verificar a integração.',
      channels: ['in_app']
    })
    
    if (result.success) {
      console.log('✅ Teste do NotificationDispatcher passou!')
      console.log('ID da notificação:', result.notificationId)
      console.log('Resultados por canal:', result.channelResults)
    } else {
      console.error('❌ Teste do NotificationDispatcher falhou:', result.errors)
    }
    
    return result.success
  } catch (error) {
    console.error('💥 Erro no teste do NotificationDispatcher:', error)
    return false
  }
}

/**
 * Teste de importação dos serviços de billing
 */
export async function testBillingServiceImport() {
  console.log('🧪 Testando importação dos serviços de billing...')
  
  try {
    const { processPaymentReminders, processOverdueNotifications } = await import('../billing/notification-service')
    
    console.log('✅ Importação dos serviços de billing bem-sucedida!')
    console.log('Funções disponíveis:', {
      processPaymentReminders: typeof processPaymentReminders,
      processOverdueNotifications: typeof processOverdueNotifications
    })
    
    return true
  } catch (error) {
    console.error('❌ Erro na importação dos serviços de billing:', error)
    return false
  }
}

/**
 * Teste de templates padrão
 */
export async function testDefaultTemplates() {
  console.log('🧪 Testando templates padrão...')
  
  try {
    const { DEFAULT_TEMPLATES } = await import('./templates/default-templates')
    
    console.log('✅ Templates padrão carregados com sucesso!')
    console.log(`Total de templates: ${DEFAULT_TEMPLATES.length}`)
    
    // Verificar se temos os templates necessários para as cron jobs
    const paymentTemplates = DEFAULT_TEMPLATES.filter(t => t.type === 'payment')
    const systemTemplates = DEFAULT_TEMPLATES.filter(t => t.type === 'system')
    
    console.log(`Templates de payment: ${paymentTemplates.length}`)
    console.log(`Templates de system: ${systemTemplates.length}`)
    
    // Verificar templates específicos
    const requiredTemplates = [
      'Lembrete Pagamento Próximo - In-App',
      'Lembrete Pagamento Hoje - In-App',
      'Lembrete Pagamento Atrasado - In-App',
      'Atraso Nível 1 (3 dias) - In-App',
      'Atraso Nível 2 (7 dias) - In-App',
      'Atraso Nível 3 (15 dias) - In-App',
      'Alerta Admin - Atrasos Críticos'
    ]
    
    const missingTemplates = requiredTemplates.filter(name => 
      !DEFAULT_TEMPLATES.some(t => t.name === name)
    )
    
    if (missingTemplates.length === 0) {
      console.log('✅ Todos os templates necessários estão presentes!')
    } else {
      console.warn('⚠️ Templates faltando:', missingTemplates)
    }
    
    return missingTemplates.length === 0
  } catch (error) {
    console.error('❌ Erro ao carregar templates padrão:', error)
    return false
  }
}

/**
 * Executa todos os testes
 */
export async function runAllTests() {
  console.log('🚀 Iniciando testes de integração das cron jobs...\n')
  
  const tests = [
    { name: 'NotificationDispatcher', test: testNotificationDispatcher },
    { name: 'Billing Service Import', test: testBillingServiceImport },
    { name: 'Default Templates', test: testDefaultTemplates }
  ]
  
  const results = []
  
  for (const { name, test } of tests) {
    console.log(`\n${'='.repeat(50)}`)
    console.log(`Executando teste: ${name}`)
    console.log('='.repeat(50))
    
    const result = await test()
    results.push({ name, passed: result })
    
    if (result) {
      console.log(`✅ ${name}: PASSOU`)
    } else {
      console.log(`❌ ${name}: FALHOU`)
    }
  }
  
  console.log(`\n${'='.repeat(50)}`)
  console.log('RESUMO DOS TESTES')
  console.log('='.repeat(50))
  
  const passed = results.filter(r => r.passed).length
  const total = results.length
  
  results.forEach(({ name, passed }) => {
    console.log(`${passed ? '✅' : '❌'} ${name}`)
  })
  
  console.log(`\n📊 Resultado: ${passed}/${total} testes passaram`)
  
  if (passed === total) {
    console.log('🎉 Todos os testes passaram! A integração está funcionando corretamente.')
  } else {
    console.log('⚠️ Alguns testes falharam. Verifique os logs acima para mais detalhes.')
  }
  
  return passed === total
}

// Executar testes se este arquivo for executado diretamente
if (require.main === module) {
  runAllTests().then(success => {
    process.exit(success ? 0 : 1)
  }).catch(error => {
    console.error('💥 Erro crítico nos testes:', error)
    process.exit(1)
  })
}
